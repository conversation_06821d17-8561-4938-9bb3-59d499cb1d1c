package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 询问通知书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("inquiryNoticeDocument")
public class InquiryNoticeDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "15询问通知书.docx";
    }

    @Override
    public String getDocumentType() {
        return "INQUIRY-NOTICE";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("FWZXZDTBSJYFWZXSJS", "service_center_sync_flag");
        mapping.put("DDSJJZ", "arrival_time_end");
        mapping.put("DWJC", "org_short_name");
        mapping.put("XGR", "modifier");
        mapping.put("WSHQ", "full_doc_no");
        mapping.put("XGSJ", "modify_time");
        mapping.put("ZY", "occupation");
        mapping.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
        mapping.put("XTCJSJCXBYDX", "sys_create_time");
        mapping.put("AY", "case_reason");
        mapping.put("SJBM", "city_org_code");
        mapping.put("FWZXZDTBSJYFWZXSJG", "service_center_sync_update");
        mapping.put("KZZD1", "ext1");
        mapping.put("BAR", "case_handler");
        mapping.put("XWTZSBS", "inquiry_notice_id");
        mapping.put("KZZD3", "ext3");
        mapping.put("GZDW", "work_unit");
        mapping.put("XTGXSJCXBYDX", "sys_modify_time");
        mapping.put("LXDH", "contact_phone");
        mapping.put("ND", "year");
        mapping.put("BZ", "remark");
        mapping.put("AJBS", "case_uuid");
        mapping.put("SFYX", "is_active");
        mapping.put("ZZ", "address");
        mapping.put("KZZD2", "ext2");
        mapping.put("DSR", "party_name");
        mapping.put("WSH", "doc_no");
        mapping.put("CJSJ", "create_time");
        mapping.put("WSRQ", "doc_date");
        mapping.put("WSZT", "doc_status");
        mapping.put("XWDD", "inquiry_location");
        mapping.put("MCRKSJ", "mc_storage_time");
        mapping.put("SFZHM", "id_card_no");
        mapping.put("DWSXZ", "org_abbr");
        mapping.put("NL", "age");
        mapping.put("CJR", "creator");
        mapping.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
        mapping.put("DDSJKS", "arrival_time_start");
        mapping.put("XYWYBS", "industry_unique_id");
        mapping.put("XB", "gender");
        mapping.put("SJMC", "city_org_name");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type == 1) {
            Map<String, Object> query = new HashMap<>();
//            query.put("AJBS", caseId);
            query.put("AJBS", "d56b3b10528848b4bb57639a337aa2de");
//			query.put("PAGESIZE", 20);

            JSONArray array = icaseInfoService.getInquiryNoticeDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });

                    // 对处理后的数据进行字段映射修正
                    return fixFieldMapping(processData);
                }
            }
        }

        // 基础信息
        mockData.put("inquiry_notice_id", "xwtzs001");
        mockData.put("case_uuid", "24909616d4b042d8bb4b7e693382e9bb");
        mockData.put("org_short_name", "广东省惠州市博罗县");
        mockData.put("full_doc_no", "博烟询﹝2025﹞第001号");
        mockData.put("doc_no", "001");
        mockData.put("doc_date", "2025/8/28");
        mockData.put("year", "2025");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("gender", 1); // 1-男性，0-女性
        mockData.put("age", 32);
        mockData.put("occupation", "个体工商户");
        mockData.put("work_unit", "博罗县龙溪隆胜轩茶烟酒商行");
        mockData.put("id_card_no", "******************");
        mockData.put("address", "广东省博罗县龙溪街道长湖村合湖小组193号");
        mockData.put("contact_phone", "13640736270");

        // 案件信息
        mockData.put("case_reason", "未在当地烟草专卖批发企业进货");
        mockData.put("case_handler", "叶辉明,朱兆强");
        mockData.put("inquiry_location", "广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号");

        // 询问时间信息
        mockData.put("inquiry_year", "2025");
        mockData.put("inquiry_month", "9");
        mockData.put("inquiry_day", "15");
        mockData.put("inquiry_hour", "9");
        mockData.put("inquiry_minute", "30");

        // 局地址信息
        mockData.put("bureau_address", "广东省惠州市博罗县龙溪街道龙桥大道政务服务中心");

        // 携带材料清单
        mockData.put("material_1", "本人身份证原件及复印件");
        mockData.put("material_2", "烟草专卖零售许可证原件及复印件");
        mockData.put("material_3", "营业执照原件及复印件");

        // 联系人信息
        mockData.put("contact_person", "叶辉明");
        mockData.put("contact_phone_bureau", "0752-6234567");

        // 落款信息
        mockData.put("seal_org", "广东省惠州市博罗县烟草专卖局");
        mockData.put("seal_year", "2025");
        mockData.put("seal_month", "8");
        mockData.put("seal_day", "28");

        // 承办人信息
        mockData.put("handler_1_name", "叶辉明");
        mockData.put("handler_1_cert_no", "19090352015");
        mockData.put("handler_1_sign_year", "2025");
        mockData.put("handler_1_sign_month", "8");
        mockData.put("handler_1_sign_day", "28");

        mockData.put("handler_2_name", "朱兆强");
        mockData.put("handler_2_cert_no", "19090352023");
        mockData.put("handler_2_sign_year", "2025");
        mockData.put("handler_2_sign_month", "8");
        mockData.put("handler_2_sign_day", "28");

        // 当事人意见及送达信息
        mockData.put("party_opinion", "");
        mockData.put("delivery_method", "直接送达");
        mockData.put("delivery_location", "当事人经营场所");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/8/28 10:30");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/8/28 10:30");
        mockData.put("sys_create_time", "2025/8/28 10:30");
        mockData.put("sys_modify_time", "2025/8/28 10:30");
        mockData.put("mc_storage_time", "2025/8/28 10:30");

        // 组织信息
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");
        mockData.put("org_abbr", "博罗");

        // 文书状态
        mockData.put("doc_status", "已生成");

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");
        mockData.put("remark", "");
        mockData.put("industry_unique_id", "tid001");
        mockData.put("service_center_sync_flag", 0);
        mockData.put("service_center_sync_update", 0);

        return mockData;
    }

    /**
     * 修正字段映射，确保模板字段能正确匹配
     * @param processData 处理后的数据
     * @return 修正后的数据
     */
    private Map<String, Object> fixFieldMapping(Map<String, Object> processData) {
        Map<String, Object> fixedData = new HashMap<>(processData);

        // 修正年份字段映射：year -> inquiry_year
        if (fixedData.get("year") != null) {
            fixedData.put("inquiry_year", fixedData.get("year"));
        }

        // 修正联系电话字段映射：contact_phone -> contact_phone_bureau
        if (fixedData.get("contact_phone") != null) {
            fixedData.put("contact_phone_bureau", fixedData.get("contact_phone"));
        }

        // 处理承办人信息，从case_handler提取contact_person
        if (fixedData.get("case_handler") != null) {
            String handlerStr = fixedData.get("case_handler").toString();
            String[] handlers = handlerStr.split(",");
            if (handlers.length > 0) {
                String firstHandler = handlers[0].trim();
                // 提取姓名（去掉括号内容）
                if (firstHandler.contains("(")) {
                    firstHandler = firstHandler.substring(0, firstHandler.indexOf("("));
                }
                fixedData.put("contact_person", firstHandler);
            }
        }

        // 设置默认的询问时间（如果没有具体时间的话）
        if (fixedData.get("inquiry_month") == null) {
            java.time.LocalDateTime futureTime = java.time.LocalDateTime.now().plusDays(7);
            fixedData.put("inquiry_month", String.valueOf(futureTime.getMonthValue()));
            fixedData.put("inquiry_day", String.valueOf(futureTime.getDayOfMonth()));
            fixedData.put("inquiry_hour", "9");
            fixedData.put("inquiry_minute", "30");
        }

        // 设置默认的局地址
        if (fixedData.get("bureau_address") == null && fixedData.get("org_short_name") != null) {
            fixedData.put("bureau_address", fixedData.get("org_short_name") + "办公地址");
        }

        // 设置默认的携带材料
        if (fixedData.get("material_1") == null) {
            fixedData.put("material_1", "本人身份证原件及复印件");
        }
        if (fixedData.get("material_2") == null) {
            fixedData.put("material_2", "烟草专卖零售许可证原件及复印件");
        }
        if (fixedData.get("material_3") == null) {
            fixedData.put("material_3", "营业执照原件及复印件");
        }

        // 设置落款时间
        java.time.LocalDate today = java.time.LocalDate.now();
        fixedData.put("seal_year", String.valueOf(today.getYear()));
        fixedData.put("seal_month", String.valueOf(today.getMonthValue()));
        fixedData.put("seal_day", String.valueOf(today.getDayOfMonth()));

        // 处理承办人详细信息
        processHandlerDetails(fixedData);

        // 设置默认送达信息
        if (fixedData.get("party_opinion") == null) {
            fixedData.put("party_opinion", "");
        }
        if (fixedData.get("delivery_method") == null) {
            fixedData.put("delivery_method", "直接送达");
        }
        if (fixedData.get("delivery_location") == null) {
            fixedData.put("delivery_location", "当事人经营场所");
        }

        return fixedData;
    }

    /**
     * 处理承办人详细信息
     */
    private void processHandlerDetails(Map<String, Object> data) {
        Object caseHandler = data.get("case_handler");
        if (caseHandler != null) {
            String handlerStr = caseHandler.toString();
            String[] handlers = handlerStr.split(",");

            // 处理第一个承办人
            if (handlers.length >= 1) {
                String handler1 = handlers[0].trim();
                if (handler1.contains("(") && handler1.contains(")")) {
                    int startIndex = handler1.indexOf("(");
                    int endIndex = handler1.indexOf(")");
                    data.put("handler_1_name", handler1.substring(0, startIndex));
                    data.put("handler_1_cert_no", handler1.substring(startIndex + 1, endIndex));
                } else {
                    data.put("handler_1_name", handler1);
                    data.put("handler_1_cert_no", "");
                }
            }

            // 处理第二个承办人
            if (handlers.length >= 2) {
                String handler2 = handlers[1].trim();
                if (handler2.contains("(") && handler2.contains(")")) {
                    int startIndex = handler2.indexOf("(");
                    int endIndex = handler2.indexOf(")");
                    data.put("handler_2_name", handler2.substring(0, startIndex));
                    data.put("handler_2_cert_no", handler2.substring(startIndex + 1, endIndex));
                } else {
                    data.put("handler_2_name", handler2);
                    data.put("handler_2_cert_no", "");
                }
            }
        }

        // 设置承办人签字时间
        java.time.LocalDate today = java.time.LocalDate.now();
        data.put("handler_1_sign_year", String.valueOf(today.getYear()));
        data.put("handler_1_sign_month", String.valueOf(today.getMonthValue()));
        data.put("handler_1_sign_day", String.valueOf(today.getDayOfMonth()));
        data.put("handler_2_sign_year", String.valueOf(today.getYear()));
        data.put("handler_2_sign_month", String.valueOf(today.getMonthValue()));
        data.put("handler_2_sign_day", String.valueOf(today.getDayOfMonth()));
    }

    /**
     * 填充缺失数据并格式化（保留原方法以防其他地方使用）
     * @param processData 处理后的数据
     * @return 填充后的数据
     */
    private Map<String, Object> fillMissingDataAndFormat(Map<String, Object> processData) {
        Map<String, Object> filledData = new HashMap<>(processData);

        // 处理时间格式化
        formatTimeFields(filledData);

        // 填充缺失的必要字段
        fillMissingFields(filledData);

        // 处理性别字段
        formatGenderField(filledData);

        // 处理询问时间相关字段
        formatInquiryTimeFields(filledData);

        // 处理案件承办人信息
        formatCaseHandlerFields(filledData);

        return filledData;
    }

    /**
     * 格式化时间字段
     */
    private void formatTimeFields(Map<String, Object> data) {
        // 处理文书日期 WSRQ (时间戳转日期)
        if (data.get("doc_date") instanceof Number) {
            long timestamp = ((Number) data.get("doc_date")).longValue();
            java.time.LocalDate date = java.time.Instant.ofEpochMilli(timestamp)
                .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            data.put("doc_date", date.format(java.time.format.DateTimeFormatter.ofPattern("yyyy/M/d")));
        }

        // 处理到达时间开始 DDSJKS
        if (data.get("arrival_time_start") instanceof Number) {
            long timestamp = ((Number) data.get("arrival_time_start")).longValue();
            java.time.LocalDateTime dateTime = java.time.Instant.ofEpochMilli(timestamp)
                .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
            data.put("arrival_time_start", dateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy年M月d日 HH:mm")));
        }

        // 处理到达时间结束 DDSJJZ
        if (data.get("arrival_time_end") instanceof Number) {
            long timestamp = ((Number) data.get("arrival_time_end")).longValue();
            java.time.LocalDateTime dateTime = java.time.Instant.ofEpochMilli(timestamp)
                .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
            data.put("arrival_time_end", dateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy年M月d日 HH:mm")));
        }
    }

    /**
     * 填充缺失字段
     */
    private void fillMissingFields(Map<String, Object> data) {
        // 如果职业为空，根据工作单位推断
        if (data.get("occupation") == null && data.get("work_unit") != null) {
            String workUnit = data.get("work_unit").toString();
            if (workUnit.contains("商行") || workUnit.contains("商店") || workUnit.contains("超市")) {
                data.put("occupation", "个体工商户");
            }
        }

        // 如果年龄为空，尝试从身份证号计算
        if (data.get("age") == null && data.get("id_card_no") != null) {
            String idCard = data.get("id_card_no").toString();
            if (idCard.length() >= 14) {
                try {
                    int birthYear = Integer.parseInt(idCard.substring(6, 10));
                    int currentYear = java.time.LocalDate.now().getYear();
                    data.put("age", currentYear - birthYear);
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }

        // 如果性别为空，尝试从身份证号获取
        if (data.get("gender") == null && data.get("id_card_no") != null) {
            String idCard = data.get("id_card_no").toString();
            if (idCard.length() >= 17) {
                try {
                    int genderCode = Integer.parseInt(idCard.substring(16, 17));
                    data.put("gender", genderCode % 2); // 奇数为男(1)，偶数为女(0)
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }

        // 填充默认的询问地点（如果为空）
        if (data.get("inquiry_location") == null && data.get("org_short_name") != null) {
            data.put("inquiry_location", data.get("org_short_name") + "询问室");
        }

        // 填充默认的携带材料
        if (data.get("material_1") == null) {
            data.put("material_1", "本人身份证原件及复印件");
        }
        if (data.get("material_2") == null) {
            data.put("material_2", "烟草专卖零售许可证原件及复印件");
        }
        if (data.get("material_3") == null) {
            data.put("material_3", "营业执照原件及复印件");
        }
    }

    /**
     * 格式化性别字段
     */
    private void formatGenderField(Map<String, Object> data) {
        Object gender = data.get("gender");
        if (gender instanceof Number) {
            int genderCode = ((Number) gender).intValue();
            data.put("gender_text", genderCode == 1 ? "男" : "女");
        }
    }

    /**
     * 格式化询问时间相关字段
     */
    private void formatInquiryTimeFields(Map<String, Object> data) {
        // 如果没有具体的询问时间，使用默认时间
        if (data.get("inquiry_year") == null) {
            java.time.LocalDateTime futureTime = java.time.LocalDateTime.now().plusDays(7);
            data.put("inquiry_year", String.valueOf(futureTime.getYear()));
            data.put("inquiry_month", String.valueOf(futureTime.getMonthValue()));
            data.put("inquiry_day", String.valueOf(futureTime.getDayOfMonth()));
            data.put("inquiry_hour", "9");
            data.put("inquiry_minute", "30");
        }

        // 设置落款时间
        java.time.LocalDate today = java.time.LocalDate.now();
        data.put("seal_year", String.valueOf(today.getYear()));
        data.put("seal_month", String.valueOf(today.getMonthValue()));
        data.put("seal_day", String.valueOf(today.getDayOfMonth()));

        // 设置承办人签字时间
        data.put("handler_1_sign_year", String.valueOf(today.getYear()));
        data.put("handler_1_sign_month", String.valueOf(today.getMonthValue()));
        data.put("handler_1_sign_day", String.valueOf(today.getDayOfMonth()));
        data.put("handler_2_sign_year", String.valueOf(today.getYear()));
        data.put("handler_2_sign_month", String.valueOf(today.getMonthValue()));
        data.put("handler_2_sign_day", String.valueOf(today.getDayOfMonth()));
    }

    /**
     * 处理案件承办人字段
     */
    private void formatCaseHandlerFields(Map<String, Object> data) {
        Object caseHandler = data.get("case_handler");
        if (caseHandler != null) {
            String handlerStr = caseHandler.toString();

            // 解析承办人信息，格式可能是 "张三(1909),李四(19)" 或 "张三,李四"
            String[] handlers = handlerStr.split(",");

            if (handlers.length >= 1) {
                String handler1 = handlers[0].trim();
                // 提取姓名和证件号
                if (handler1.contains("(") && handler1.contains(")")) {
                    int startIndex = handler1.indexOf("(");
                    int endIndex = handler1.indexOf(")");
                    data.put("handler_1_name", handler1.substring(0, startIndex));
                    data.put("handler_1_cert_no", handler1.substring(startIndex + 1, endIndex));
                } else {
                    data.put("handler_1_name", handler1);
                    data.put("handler_1_cert_no", ""); // 默认空值
                }
            }

            if (handlers.length >= 2) {
                String handler2 = handlers[1].trim();
                // 提取姓名和证件号
                if (handler2.contains("(") && handler2.contains(")")) {
                    int startIndex = handler2.indexOf("(");
                    int endIndex = handler2.indexOf(")");
                    data.put("handler_2_name", handler2.substring(0, startIndex));
                    data.put("handler_2_cert_no", handler2.substring(startIndex + 1, endIndex));
                } else {
                    data.put("handler_2_name", handler2);
                    data.put("handler_2_cert_no", ""); // 默认空值
                }
            }

            // 设置联系人为第一个承办人
            if (data.get("contact_person") == null && data.get("handler_1_name") != null) {
                data.put("contact_person", data.get("handler_1_name"));
            }
        }

        // 设置默认联系电话（如果没有的话）
        if (data.get("contact_phone_bureau") == null && data.get("contact_phone") != null) {
            data.put("contact_phone_bureau", data.get("contact_phone"));
        }

        // 设置印章机关
        if (data.get("seal_org") == null && data.get("org_short_name") != null) {
            data.put("seal_org", data.get("org_short_name") + "烟草专卖局");
        }

        // 设置送达方式默认值
        if (data.get("delivery_method") == null) {
            data.put("delivery_method", "直接送达");
        }
        if (data.get("delivery_location") == null) {
            data.put("delivery_location", "当事人经营场所");
        }
        if (data.get("party_opinion") == null) {
            data.put("party_opinion", "");
        }
    }
}
